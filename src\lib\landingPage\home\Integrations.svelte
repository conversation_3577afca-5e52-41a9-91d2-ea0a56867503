<script>
    import { H2, H4, P1, P2, Button } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';
    
    export let sectionLabel = "Integrations";
    export let headline = "Direct integrations with your favorite tools";
    export let subheadline = "Describe what these integrations help your customers do and impressive facts about how they work.";
    export let integrations = [
        { icon: "📷", name: "Instagram" },
        { icon: "🐦", name: "Twitter" },
        { icon: "💼", name: "LinkedIn" }
    ];
    export let primaryButton = "Get Started";
    export let secondaryButton = "See all Integrations";
    export let imagePlaceholder = "INTEGRATIONS IMAGE";
</script>

<SectionWrapper --bg-color="var(--tangerine)" --padding-top="8rem" --padding-bottom="8rem">
<div class="integrations-section">
    <div class="integrations-header">
        <P2>{sectionLabel}</P2>
        <H2>{headline}</H2>
        <P1>{subheadline}</P1>
    </div>
    
    <div class="integrations-grid">
        {#each integrations as integration}
            <div class="integration-card">
                <div class="integration-icon">{integration.icon}</div>
                <H4>{integration.name}</H4>
            </div>
        {/each}
    </div>
    
    <div class="integrations-buttons">
        <Button>{primaryButton}</Button>
        <Button isSecondary>{secondaryButton}</Button>
    </div>
    
    <div class="integrations-image">
        <div class="placeholder-image">
            <P2>{imagePlaceholder}</P2>
        </div>
    </div>
</div>
</SectionWrapper>

<style>
    /* Integrations Section */
    .integrations-section {
        max-width: 1200px;
        width: 100%;
        text-align: center;
    }
    
    .integrations-header {
        margin-bottom: 4rem;
    }
    
    .integrations-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
        margin-bottom: 3rem;
    }
    
    .integration-card {
        background: white;
        border: 4px solid var(--pitch-black);
        border-radius: 1rem;
        padding: 2rem;
        text-align: center;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }
    
    .integration-card:hover {
        transform: translate(-4px, -4px);
        box-shadow: 8px 8px 0 var(--pitch-black);
    }
    
    .integration-icon {
        font-size: 3rem;
        background: var(--aquamarine);
        width: 4rem;
        height: 4rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 3px solid var(--pitch-black);
    }
    
    .integrations-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
        margin-bottom: 3rem;
    }
    
    .integrations-image {
        display: flex;
        justify-content: center;
    }
    
    .placeholder-image {
        width: 100%;
        max-width: 600px;
        height: 300px;
        background: var(--pitch-black);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 4px solid var(--pitch-black);
        font-weight: 700;
    }
    
    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .integrations-grid {
            grid-template-columns: 1fr;
        }
        
        .placeholder-image {
            height: 200px;
        }
    }
</style>
